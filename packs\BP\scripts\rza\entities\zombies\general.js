export function transformToWalker(entity, dimension, location) {
    try {
        const walker = dimension.spawnEntity('rza:walker', location);
        entity.remove();
        walker.triggerEvent('minecraft:entity_transformed');
    }
    catch (error) {
        dimension.spawnEntity('rza:walker', location);
    }
}
export async function transformToFeral(entity, dimension, location) {
    try {
        const feral = dimension.spawnEntity('rza:feral', location);
        entity.remove();
        feral.triggerEvent('minecraft:entity_transformed');
    }
    catch (error) {
        dimension.spawnEntity('rza:feral', location);
    }
}
